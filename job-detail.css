/* Job Detail Pages Styles */

/* Back Navigation */
.back-navigation {
    padding: 40px 40px 20px 40px;
    background: rgba(0, 0, 0, 0.9);
    margin-top: 20px;
}

.back-button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    color: #FFFFFF;
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    font-weight: 400;
    font-size: 16px;
    transition: all 0.3s ease;
    padding: 10px 20px;
    border-radius: 25px;
    background: rgba(75, 0, 130, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.back-button:hover {
    background: rgba(255, 45, 85, 0.2);
    border-color: #FF2D55;
    transform: translateX(-5px);
}

.back-arrow {
    font-size: 18px;
    transition: transform 0.3s ease;
}

.back-button:hover .back-arrow {
    transform: translateX(-3px);
}

/* Job Hero Section */
.job-hero {
    background: #000000;
    padding: 80px 40px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.job-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(75, 0, 130, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 45, 85, 0.3) 0%, transparent 50%);
    z-index: 1;
}

.job-hero-content {
    position: relative;
    z-index: 2;
    max-width: 1000px;
    margin: 0 auto;
}

.company-branding {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.hero-company-logo {
    width: 160px;
    height: 160px;
    border-radius: 25px;
    border: 4px solid #FFFFFF;
    box-shadow: 0 15px 40px rgba(75, 0, 130, 0.4);
    transition: all 0.3s ease;
}

.hero-company-logo:hover {
    transform: scale(1.05);
    border-color: #FF2D55;
    box-shadow: 0 15px 40px rgba(255, 45, 85, 0.4);
}

.company-info {
    text-align: left;
}

.job-title-hero {
    font-family: 'Montserrat', sans-serif;
    font-size: 48px;
    font-weight: 700;
    color: #FFFFFF;
    margin: 0 0 10px 0;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.company-name-hero {
    font-family: 'Montserrat', sans-serif;
    font-size: 24px;
    font-weight: 400;
    color: #FF2D55;
    margin: 0 0 10px 0;
}

.job-duration-hero {
    font-family: 'Montserrat', sans-serif;
    font-size: 18px;
    font-weight: 300;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.job-summary {
    max-width: 800px;
    margin: 0 auto;
    padding: 30px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.job-summary p {
    font-family: 'Montserrat', sans-serif;
    font-size: 18px;
    line-height: 1.6;
    color: #FFFFFF;
    margin: 0;
}

/* Job Content Section */
.job-content {
    padding: 80px 40px;
    background: #000000;
}

.content-grid {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;
}

.content-card {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 30px rgba(75, 0, 130, 0.2);
    transition: all 0.3s ease;
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(75, 0, 130, 0.3);
    border-color: rgba(255, 45, 85, 0.3);
}

.content-card h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 28px;
    font-weight: 700;
    color: #FF2D55;
    margin: 0 0 20px 0;
    text-transform: uppercase;
}

.content-card h4 {
    font-family: 'Montserrat', sans-serif;
    font-size: 20px;
    font-weight: 600;
    color: #FFFFFF;
    margin: 30px 0 15px 0;
}

.content-card p {
    font-family: 'Montserrat', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20px;
}

.content-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.content-card li {
    font-family: 'Montserrat', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
}

.content-card li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: #FF2D55;
    font-weight: bold;
}

/* Skills Grid */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 20px;
}

.skill-category h4 {
    color: #4B0082;
    margin-bottom: 15px;
    font-size: 18px;
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.skill-tag {
    background: linear-gradient(135deg, #4B0082, #FF2D55);
    color: #FFFFFF;
    padding: 8px 16px;
    border-radius: 20px;
    font-family: 'Montserrat', sans-serif;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.skill-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 45, 85, 0.3);
}

/* Accomplishments */
.accomplishments-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 20px;
}

.accomplishment-item {
    text-align: center;
    padding: 30px 20px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    border: 1px solid rgba(255, 45, 85, 0.2);
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(75, 0, 130, 0.2);
}

.accomplishment-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(75, 0, 130, 0.3);
    border-color: rgba(255, 45, 85, 0.4);
}

.metric {
    font-family: 'Montserrat', sans-serif;
    font-size: 48px;
    font-weight: 700;
    color: #FF2D55;
    margin-bottom: 10px;
}

.metric-description {
    font-family: 'Montserrat', sans-serif;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
}

/* Role Projects Section */
.role-projects {
    padding: 80px 40px;
    background: linear-gradient(135deg, rgba(75, 0, 130, 0.1), rgba(255, 45, 85, 0.1));
}

.role-projects h2 {
    font-family: 'Montserrat', sans-serif;
    font-size: 36px;
    font-weight: 700;
    color: #FFFFFF;
    text-align: center;
    margin-bottom: 60px;
    text-transform: uppercase;
}

.projects-grid {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
}

.project-card {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(75, 0, 130, 0.3);
}

.project-image {
    width: 100%;
    height: 350px;
    overflow: hidden;
    border-radius: 15px 15px 0 0;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-info {
    padding: 30px;
}

.project-info h3 {
    font-family: 'Montserrat', sans-serif;
    font-size: 22px;
    font-weight: 600;
    color: #FFFFFF;
    margin: 0 0 15px 0;
}

.project-info p {
    font-family: 'Montserrat', sans-serif;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
    margin-bottom: 20px;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.project-tech span {
    background: linear-gradient(135deg, #4B0082, #FF2D55);
    color: #FFFFFF;
    padding: 5px 12px;
    border-radius: 15px;
    font-family: 'Montserrat', sans-serif;
    font-size: 12px;
    font-weight: 500;
}



/* Tablet Responsiveness */
@media (max-width: 1024px) and (min-width: 769px) {
    .project-image {
        height: 320px;
    }

    .hero-company-logo {
        width: 130px;
        height: 130px;
        border-radius: 22px;
    }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .back-navigation {
        padding: 15px 20px;
    }

    .job-hero {
        padding: 60px 20px;
    }

    .company-branding {
        flex-direction: column;
        gap: 20px;
    }

    .company-info {
        text-align: center;
    }

    .job-title-hero {
        font-size: 32px;
    }

    .company-name-hero {
        font-size: 20px;
    }

    .job-summary {
        padding: 20px;
    }

    .job-content {
        padding: 60px 20px;
    }

    .content-card {
        padding: 25px;
    }

    .content-card h3 {
        font-size: 24px;
    }

    .skills-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .accomplishments-list {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
    }

    .metric {
        font-size: 36px;
    }

    .role-projects {
        padding: 60px 20px;
    }

    .role-projects h2 {
        font-size: 28px;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .project-image {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .job-title-hero {
        font-size: 24px;
    }

    .company-name-hero {
        font-size: 18px;
    }

    .hero-company-logo {
        width: 100px;
        height: 100px;
        border-radius: 18px;
        border: 3px solid #FFFFFF;
    }

    .content-card {
        padding: 20px;
    }

    .accomplishments-list {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .project-image {
        height: 280px;
    }
}
